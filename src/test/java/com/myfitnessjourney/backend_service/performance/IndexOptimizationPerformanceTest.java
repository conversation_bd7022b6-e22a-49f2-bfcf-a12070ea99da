package com.myfitnessjourney.backend_service.performance;

import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.repository.OptimizedFoodNutrientsRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Performance test suite to validate index optimization improvements.
 * 
 * This test compares the performance of original queries vs optimized queries
 * using the new covering indexes and read-heavy optimizations.
 * 
 * Run this test before and after applying migration 013 to measure improvements.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class IndexOptimizationPerformanceTest {

    @Autowired
    private FoodNutrientsRepository originalRepository;

    @Autowired
    private OptimizedFoodNutrientsRepository optimizedRepository;

    private static final String[] TEST_QUERIES = {
        "apple",
        "chicken breast",
        "coca cola",
        "bread",
        "milk",
        "banana",
        "rice",
        "pasta",
        "cheese",
        "yogurt"
    };

    private static final String[] TEST_PREFIXES = {
        "app",
        "chi",
        "coc",
        "bre",
        "mil"
    };

    private static final String[] TEST_CATEGORIES = {
        "beverages",
        "dairy",
        "meat",
        "fruits",
        "vegetables"
    };

    @Test
    public void testFullTextSearchPerformance() {
        System.out.println("\n=== Full-Text Search Performance Test ===");
        
        for (String query : TEST_QUERIES) {
            // Test original query
            long startTime = System.nanoTime();
            var originalResults = originalRepository.searchFoodByText(query);
            long originalTime = System.nanoTime() - startTime;
            
            // Test optimized query (if available)
            startTime = System.nanoTime();
            var optimizedResults = optimizedRepository.searchFoodOptimizedCovering(query);
            long optimizedTime = System.nanoTime() - startTime;
            
            double improvementPercent = ((double)(originalTime - optimizedTime) / originalTime) * 100;
            
            System.out.printf("Query: %-15s | Original: %6.2fms | Optimized: %6.2fms | Improvement: %6.2f%%\n",
                query,
                originalTime / 1_000_000.0,
                optimizedTime / 1_000_000.0,
                improvementPercent
            );
        }
    }

    @Test
    public void testExactCodeLookupPerformance() {
        System.out.println("\n=== Exact Code Lookup Performance Test ===");
        
        // Get some actual codes from the database first
        var sampleCodes = originalRepository.findAll().stream()
            .limit(10)
            .map(food -> food.getCode())
            .toList();
        
        for (String code : sampleCodes) {
            // Test original query
            long startTime = System.nanoTime();
            var originalResults = originalRepository.findByExactCode(code);
            long originalTime = System.nanoTime() - startTime;
            
            // Test optimized query
            startTime = System.nanoTime();
            var optimizedResults = optimizedRepository.findByExactCodeOptimized(code);
            long optimizedTime = System.nanoTime() - startTime;
            
            double improvementPercent = ((double)(originalTime - optimizedTime) / originalTime) * 100;
            
            System.out.printf("Code: %-15s | Original: %6.2fms | Optimized: %6.2fms | Improvement: %6.2f%%\n",
                code.substring(0, Math.min(code.length(), 12)) + "...",
                originalTime / 1_000_000.0,
                optimizedTime / 1_000_000.0,
                improvementPercent
            );
        }
    }

    @Test
    public void testAutocompletePrefixPerformance() {
        System.out.println("\n=== Autocomplete Prefix Performance Test ===");
        
        for (String prefix : TEST_PREFIXES) {
            // Test original query
            long startTime = System.nanoTime();
            var originalResults = originalRepository.findByProductNamePrefix(prefix);
            long originalTime = System.nanoTime() - startTime;
            
            // Test optimized query
            startTime = System.nanoTime();
            var optimizedResults = optimizedRepository.findByProductNamePrefixOptimized(prefix);
            long optimizedTime = System.nanoTime() - startTime;
            
            double improvementPercent = ((double)(originalTime - optimizedTime) / originalTime) * 100;
            
            System.out.printf("Prefix: %-10s | Original: %6.2fms | Optimized: %6.2fms | Improvement: %6.2f%%\n",
                prefix,
                originalTime / 1_000_000.0,
                optimizedTime / 1_000_000.0,
                improvementPercent
            );
        }
    }

    @Test
    public void testCategorySearchPerformance() {
        System.out.println("\n=== Category Search Performance Test ===");
        
        for (String category : TEST_CATEGORIES) {
            // Test optimized category search
            long startTime = System.nanoTime();
            var optimizedResults = optimizedRepository.findByCategoryOptimized(category);
            long optimizedTime = System.nanoTime() - startTime;
            
            System.out.printf("Category: %-12s | Optimized: %6.2fms | Results: %d\n",
                category,
                optimizedTime / 1_000_000.0,
                optimizedResults.size()
            );
        }
    }

    @Test
    public void testNutritionRangeSearchPerformance() {
        System.out.println("\n=== Nutrition Range Search Performance Test ===");
        
        // Test various nutrition ranges
        double[][] nutritionRanges = {
            {100, 300, 10}, // Low calorie, moderate protein
            {200, 500, 20}, // Moderate calorie, high protein
            {50, 200, 5},   // Very low calorie, low protein
            {300, 600, 15}, // High calorie, moderate protein
        };
        
        for (double[] range : nutritionRanges) {
            long startTime = System.nanoTime();
            var results = optimizedRepository.findByNutritionRangeOptimized(range[0], range[1], range[2]);
            long queryTime = System.nanoTime() - startTime;
            
            System.out.printf("Range: %3.0f-%3.0f cal, %2.0f+ protein | Time: %6.2fms | Results: %d\n",
                range[0], range[1], range[2],
                queryTime / 1_000_000.0,
                results.size()
            );
        }
    }

    @Test
    public void testHybridSearchPerformance() {
        System.out.println("\n=== Hybrid Search Performance Test ===");
        
        for (String query : TEST_QUERIES) {
            // Test original optimized query
            long startTime = System.nanoTime();
            var originalResults = originalRepository.searchFoodOptimized(query);
            long originalTime = System.nanoTime() - startTime;
            
            // Test new hybrid optimized query
            startTime = System.nanoTime();
            var hybridResults = optimizedRepository.searchFoodHybridOptimized(query);
            long hybridTime = System.nanoTime() - startTime;
            
            double improvementPercent = ((double)(originalTime - hybridTime) / originalTime) * 100;
            
            System.out.printf("Query: %-15s | Original: %6.2fms | Hybrid: %6.2fms | Improvement: %6.2f%%\n",
                query,
                originalTime / 1_000_000.0,
                hybridTime / 1_000_000.0,
                improvementPercent
            );
        }
    }

    @Test
    public void testIndexUsageStatistics() {
        System.out.println("\n=== Index Usage Statistics ===");
        
        // Run some queries to generate index usage
        for (String query : TEST_QUERIES) {
            optimizedRepository.searchFoodOptimizedCovering(query);
            optimizedRepository.searchFoodFuzzyOptimized(query);
        }
        
        // Get index usage statistics
        var indexStats = optimizedRepository.getIndexUsageStatistics();
        
        System.out.println("Index Name                                    | Scans    | Tuples Read | Tuples Fetched | Efficiency %");
        System.out.println("---------------------------------------------|----------|-------------|----------------|-------------");
        
        for (Object[] stat : indexStats) {
            System.out.printf("%-45s | %-8s | %-11s | %-14s | %-11s\n",
                stat[0], stat[1], stat[2], stat[3], stat[4]
            );
        }
    }

    @Test
    public void testConcurrentSearchPerformance() {
        System.out.println("\n=== Concurrent Search Performance Test ===");
        
        int threadCount = 10;
        int queriesPerThread = 100;
        
        // Test concurrent performance with optimized queries
        long startTime = System.currentTimeMillis();
        
        Thread[] threads = new Thread[threadCount];
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            threads[i] = new Thread(() -> {
                for (int j = 0; j < queriesPerThread; j++) {
                    String query = TEST_QUERIES[j % TEST_QUERIES.length];
                    optimizedRepository.searchFoodOptimizedCovering(query);
                }
            });
            threads[i].start();
        }
        
        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        long totalTime = System.currentTimeMillis() - startTime;
        int totalQueries = threadCount * queriesPerThread;
        double avgTimePerQuery = (double) totalTime / totalQueries;
        double queriesPerSecond = 1000.0 / avgTimePerQuery;
        
        System.out.printf("Concurrent Performance Results:\n");
        System.out.printf("- Threads: %d\n", threadCount);
        System.out.printf("- Queries per thread: %d\n", queriesPerThread);
        System.out.printf("- Total queries: %d\n", totalQueries);
        System.out.printf("- Total time: %d ms\n", totalTime);
        System.out.printf("- Average time per query: %.2f ms\n", avgTimePerQuery);
        System.out.printf("- Queries per second: %.2f\n", queriesPerSecond);
    }

    @Test
    public void printPerformanceSummary() {
        System.out.println("\n" + "=".repeat(80));
        System.out.println("INDEX OPTIMIZATION PERFORMANCE SUMMARY");
        System.out.println("=".repeat(80));
        System.out.println("Before running this test:");
        System.out.println("1. Apply migration 013-optimize-indexes-for-read-heavy-workload.yaml");
        System.out.println("2. Ensure your database has sufficient data (3.8M records)");
        System.out.println("3. Run ANALYZE on the food_nutrients table");
        System.out.println("");
        System.out.println("Expected improvements:");
        System.out.println("- Full-text search: 40-60% faster with covering indexes");
        System.out.println("- Exact code lookup: 20-30% faster (already optimized with unique constraint)");
        System.out.println("- Autocomplete: 50-70% faster with specialized prefix index");
        System.out.println("- Category search: 30-50% faster with enhanced GIN index");
        System.out.println("- Nutrition filtering: 60-80% faster with covering index");
        System.out.println("- Hybrid search: 40-60% faster with optimized index usage");
        System.out.println("=".repeat(80));
    }
}
