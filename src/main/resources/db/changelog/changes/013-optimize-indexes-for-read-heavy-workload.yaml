databaseChangeLog:
  - changeSet:
      id: 013-optimize-indexes-for-read-heavy-workload
      author: f<PERSON><PERSON><PERSON><PERSON>
      comment: "Optimize indexes for read-heavy food search workload - remove redundant indexes and implement covering indexes"
      changes:
        # Phase 1: Remove unused partitioned table and its 40 indexes
        - sql:
            sql: |
              -- Drop unused partitioned table and all its indexes (40 indexes total)
              -- This table was created but never used by the application
              DROP TABLE IF EXISTS food_nutrients_partitioned CASCADE;

        # Phase 2: Remove redundant indexes on main table
        - sql:
            sql: |
              -- Remove redundant indexes that provide minimal benefit
              -- Keep only the most effective indexes for our query patterns
              
              -- Drop redundant country_tags index (rarely used in queries)
              DROP INDEX IF EXISTS idx_country_tags;
              
              -- Drop redundant code lookup index (unique constraint already provides this)
              DROP INDEX IF EXISTS idx_food_code_lookup;
              
              -- Drop overlapping category indexes (keeping the filtered GIN version)
              DROP INDEX IF EXISTS idx_category_tags;
              
              -- Drop redundant brand index (replaced with optimized partial index)
              DROP INDEX IF EXISTS idx_brand_tag;

        # Phase 3: Create optimized covering indexes for index-only scans
        - sql:
            sql: |
              -- Covering index for search results (enables index-only scans)
              -- This index contains all columns needed for search result display
              CREATE INDEX idx_food_search_results_covering 
              ON food_nutrients (code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g)
              WHERE search_vector IS NOT NULL;

        - sql:
            sql: |
              -- Covering index for nutrition-focused searches
              -- Optimized for health-conscious users filtering by nutrition values
              CREATE INDEX idx_food_nutrition_search_covering
              ON food_nutrients (energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g, product_name, code)
              WHERE energy_kcal_100g IS NOT NULL 
                AND proteins_100g IS NOT NULL 
                AND carbohydrates_100g IS NOT NULL 
                AND fat_100g IS NOT NULL;

        # Phase 4: Create specialized indexes for exact matches
        - sql:
            sql: |
              -- Optimized index for exact product name matches (case-insensitive)
              -- Uses expression index for normalized searches
              CREATE INDEX idx_food_exact_name_normalized
              ON food_nutrients (lower(trim(product_name)))
              WHERE product_name IS NOT NULL AND length(product_name) > 0;

        - sql:
            sql: |
              -- Optimized index for autocomplete with length-based optimization
              -- Partial index for short product names (most autocomplete scenarios)
              CREATE INDEX idx_food_autocomplete_optimized
              ON food_nutrients (left(lower(product_name), 20), length(product_name))
              WHERE product_name IS NOT NULL AND length(product_name) BETWEEN 3 AND 100;

        # Phase 5: Create brand and category indexes optimized for read workloads
        - sql:
            sql: |
              -- Optimized brand search with covering columns
              -- Includes commonly accessed columns for brand-based searches
              CREATE INDEX idx_food_brand_optimized
              ON food_nutrients (brand_tag, product_name, energy_kcal_100g, proteins_100g)
              WHERE brand_tag IS NOT NULL AND length(brand_tag) > 0;

        - sql:
            sql: |
              -- Enhanced category index with array optimization
              -- Optimized for category filtering with GIN index
              CREATE INDEX idx_food_category_enhanced
              ON food_nutrients USING GIN (category_tags)
              WHERE category_tags IS NOT NULL AND array_length(category_tags, 1) > 0;

        # Phase 6: Create specialized indexes for fuzzy search optimization
        - sql:
            sql: |
              -- Optimized trigram index for fuzzy matching
              -- Enhanced with partial index for better performance
              CREATE INDEX idx_food_fuzzy_search_optimized
              ON food_nutrients USING GIN (lower(product_name) gin_trgm_ops)
              WHERE product_name IS NOT NULL AND length(product_name) >= 3;

        # Phase 7: Create composite indexes for common query patterns
        - sql:
            sql: |
              -- Composite index for category + search vector queries
              -- Optimized for filtered full-text searches
              CREATE INDEX idx_food_category_search_composite
              ON food_nutrients USING GIN (category_tags, search_vector)
              WHERE category_tags IS NOT NULL AND search_vector IS NOT NULL;

        - sql:
            sql: |
              -- Composite index for brand + nutrition filtering
              -- Optimized for brand-specific nutrition searches
              CREATE INDEX idx_food_brand_nutrition_composite
              ON food_nutrients (brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g)
              WHERE brand_tag IS NOT NULL 
                AND energy_kcal_100g IS NOT NULL 
                AND proteins_100g IS NOT NULL;

        # Phase 8: Update table statistics and optimize query planner
        - sql:
            sql: |
              -- Update table statistics for optimal query planning
              ANALYZE food_nutrients;
              
              -- Set table-specific statistics targets for better query planning
              ALTER TABLE food_nutrients ALTER COLUMN product_name SET STATISTICS 1000;
              ALTER TABLE food_nutrients ALTER COLUMN search_vector SET STATISTICS 1000;
              ALTER TABLE food_nutrients ALTER COLUMN category_tags SET STATISTICS 500;
              ALTER TABLE food_nutrients ALTER COLUMN brand_tag SET STATISTICS 500;

        # Phase 9: Create indexes for master data tables (brands, categories, countries)
        - sql:
            sql: |
              -- Optimize master data tables for read-heavy workloads
              -- These tables are read-only and can benefit from aggressive indexing
              
              -- Enhanced brand table indexes
              CREATE INDEX idx_brands_name_search ON brands USING GIN (to_tsvector('english', brand_name));
              CREATE INDEX idx_brands_tag_name_covering ON brands (brand_tag, brand_name);
              
              -- Enhanced category table indexes  
              CREATE INDEX idx_categories_name_search ON categories USING GIN (to_tsvector('english', category_name));
              CREATE INDEX idx_categories_tag_name_covering ON categories (category_tag, category_name);
              
              -- Enhanced country table indexes
              CREATE INDEX idx_countries_name_search ON countries USING GIN (to_tsvector('english', country_name));
              CREATE INDEX idx_countries_tag_name_covering ON countries (country_tag, country_name);

      rollback:
        - sql:
            sql: |
              -- Rollback: Drop all optimized indexes
              DROP INDEX IF EXISTS idx_food_search_results_covering;
              DROP INDEX IF EXISTS idx_food_nutrition_search_covering;
              DROP INDEX IF EXISTS idx_food_exact_name_normalized;
              DROP INDEX IF EXISTS idx_food_autocomplete_optimized;
              DROP INDEX IF EXISTS idx_food_brand_optimized;
              DROP INDEX IF EXISTS idx_food_category_enhanced;
              DROP INDEX IF EXISTS idx_food_fuzzy_search_optimized;
              DROP INDEX IF EXISTS idx_food_category_search_composite;
              DROP INDEX IF EXISTS idx_food_brand_nutrition_composite;
              
              -- Rollback master data table indexes
              DROP INDEX IF EXISTS idx_brands_name_search;
              DROP INDEX IF EXISTS idx_brands_tag_name_covering;
              DROP INDEX IF EXISTS idx_categories_name_search;
              DROP INDEX IF EXISTS idx_categories_tag_name_covering;
              DROP INDEX IF EXISTS idx_countries_name_search;
              DROP INDEX IF EXISTS idx_countries_tag_name_covering;
              
              -- Recreate original indexes
              CREATE INDEX idx_country_tags ON food_nutrients USING GIN (country_tags);
              CREATE INDEX idx_food_code_lookup ON food_nutrients (code);
              CREATE INDEX idx_category_tags ON food_nutrients USING GIN (category_tags);
              CREATE INDEX idx_brand_tag ON food_nutrients (brand_tag);
              
              -- Reset statistics targets
              ALTER TABLE food_nutrients ALTER COLUMN product_name SET STATISTICS DEFAULT;
              ALTER TABLE food_nutrients ALTER COLUMN search_vector SET STATISTICS DEFAULT;
              ALTER TABLE food_nutrients ALTER COLUMN category_tags SET STATISTICS DEFAULT;
              ALTER TABLE food_nutrients ALTER COLUMN brand_tag SET STATISTICS DEFAULT;
