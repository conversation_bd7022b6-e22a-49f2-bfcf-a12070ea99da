package com.myfitnessjourney.backend_service.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class CacheWarmingService {

    private final RedisTemplate<String, Object> redisTemplate;

    // Dynamic search frequency tracking
    private static final String SEARCH_FREQUENCY_PREFIX = "search_frequency:";
    private static final String POPULAR_SEARCHES_KEY = "popular_searches_sorted_set";
    private static final int POPULAR_THRESHOLD = 5; // Minimum searches to be considered popular
    private static final int POPULAR_CACHE_HOURS = 6; // Extended TTL for popular searches

    /**
     * Track search frequency for dynamic cache optimization
     */
    public void trackSearchFrequency(String query) {
        if (query == null || query.trim().isEmpty()) {
            return;
        }

        String normalizedQuery = normalizeQuery(query);
        String frequencyKey = SEARCH_FREQUENCY_PREFIX + normalizedQuery;

        try {
            // Increment search frequency counter with 24-hour TTL
            redisTemplate.opsForValue().increment(frequencyKey);
            redisTemplate.expire(frequencyKey, 24, TimeUnit.HOURS);

            // Add to sorted set for popular searches tracking
            redisTemplate.opsForZSet().incrementScore(POPULAR_SEARCHES_KEY, normalizedQuery, 1.0);
            redisTemplate.expire(POPULAR_SEARCHES_KEY, 24, TimeUnit.HOURS);

            log.debug("Tracked search frequency for: {}", normalizedQuery);
        } catch (Exception e) {
            log.warn("Failed to track search frequency for: {}", normalizedQuery, e);
        }
    }

    /**
     * Check if a query is popular enough for extended caching
     */
    public boolean isPopularSearch(String query) {
        if (query == null || query.trim().isEmpty()) {
            return false;
        }

        String normalizedQuery = normalizeQuery(query);
        String frequencyKey = SEARCH_FREQUENCY_PREFIX + normalizedQuery;

        try {
            String count = (String) redisTemplate.opsForValue().get(frequencyKey);
            return count != null && Integer.parseInt(count) >= POPULAR_THRESHOLD;
        } catch (Exception e) {
            log.debug("Failed to check popularity for: {}", normalizedQuery, e);
            return false;
        }
    }

    /**
     * Get current popular search terms
     */
    public List<String> getPopularSearchTerms(int limit) {
        try {
            // Get top searches from sorted set (highest scores first)
            var popularTerms = redisTemplate.opsForZSet()
                .reverseRange(POPULAR_SEARCHES_KEY, 0, limit - 1);

            return popularTerms != null ?
                popularTerms.stream().map(Object::toString).toList() :
                List.of();
        } catch (Exception e) {
            log.warn("Failed to get popular search terms", e);
            return List.of();
        }
    }

    /**
     * Normalize query for consistent tracking and caching
     */
    private String normalizeQuery(String query) {
        return query.toLowerCase()
                .replaceAll("[^a-z0-9\\s]", "") // Remove special characters
                .replaceAll("\\s+", " ") // Normalize whitespace
                .trim();
    }

    /**
     * Get cache statistics including popular search metrics
     */
    public PopularSearchStats getPopularSearchStats() {
        try {
            // Get total number of tracked searches
            var allPopularTerms = redisTemplate.opsForZSet().range(POPULAR_SEARCHES_KEY, 0, -1);
            int totalTrackedTerms = allPopularTerms != null ? allPopularTerms.size() : 0;

            // Get popular terms (above threshold)
            List<String> popularTerms = getPopularSearchTerms(20);

            // Count terms above threshold
            long popularCount = popularTerms.stream()
                .mapToLong(term -> {
                    try {
                        String frequencyKey = SEARCH_FREQUENCY_PREFIX + term;
                        String count = (String) redisTemplate.opsForValue().get(frequencyKey);
                        return count != null ? Long.parseLong(count) : 0;
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .filter(count -> count >= POPULAR_THRESHOLD)
                .count();

            return new PopularSearchStats(
                totalTrackedTerms,
                (int) popularCount,
                popularTerms.subList(0, Math.min(10, popularTerms.size())),
                POPULAR_THRESHOLD
            );
        } catch (Exception e) {
            log.error("Error getting popular search stats", e);
            return new PopularSearchStats(0, 0, List.of(), POPULAR_THRESHOLD);
        }
    }

    /**
     * Clear all search frequency tracking data
     */
    public void clearSearchFrequencyData() {
        try {
            // Clear frequency counters
            var frequencyKeys = redisTemplate.keys(SEARCH_FREQUENCY_PREFIX + "*");
            if (frequencyKeys != null && !frequencyKeys.isEmpty()) {
                redisTemplate.delete(frequencyKeys);
            }

            // Clear popular searches sorted set
            redisTemplate.delete(POPULAR_SEARCHES_KEY);

            log.info("Cleared all search frequency tracking data");
        } catch (Exception e) {
            log.error("Error clearing search frequency data", e);
        }
    }

    // Get cache statistics
    public CacheStats getCacheStats() {
        try {
            var searchKeys = redisTemplate.keys("food_search:*");
            var frequencyKeys = redisTemplate.keys(SEARCH_FREQUENCY_PREFIX + "*");
            var exactKeys = redisTemplate.keys("exact_match:*");

            return new CacheStats(
                searchKeys != null ? searchKeys.size() : 0,
                frequencyKeys != null ? frequencyKeys.size() : 0,
                exactKeys != null ? exactKeys.size() : 0,
                getPopularSearchStats().popularTermsCount()
            );
        } catch (Exception e) {
            log.error("Error getting cache stats", e);
            return new CacheStats(0, 0, 0, 0);
        }
    }

    public record CacheStats(
        int searchCacheSize,
        int frequencyTrackingSize,
        int exactMatchCacheSize,
        int popularTermsCount
    ) {}

    public record PopularSearchStats(
        int totalTrackedTerms,
        int popularTermsCount,
        List<String> topPopularTerms,
        int popularityThreshold
    ) {}
}
