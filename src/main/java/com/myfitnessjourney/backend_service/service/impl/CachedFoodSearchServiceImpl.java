package com.myfitnessjourney.backend_service.service.impl;

import com.myfitnessjourney.backend_service.dto.FoodSearchResponseDto;
import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import com.myfitnessjourney.backend_service.repository.FoodNutrientsRepository;
import com.myfitnessjourney.backend_service.service.CacheWarmingService;
import com.myfitnessjourney.backend_service.service.FoodSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(name = "spring.data.redis.host")
@RequiredArgsConstructor
@Slf4j
public class CachedFoodSearchServiceImpl implements FoodSearchService {

    private final FoodNutrientsRepository foodNutrientsRepository;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheWarmingService cacheWarmingService;

    // Cache popular searches for faster access
    private static final String POPULAR_SEARCH_PREFIX = "popular_search:";
    private static final String SEARCH_CACHE_PREFIX = "food_search:";
    private static final String EXACT_MATCH_PREFIX = "exact_match:";

    @Override
    public List<FoodSearchResponseDto> searchFoods(String query) {
        if (query == null || query.trim().isEmpty()) {
            return List.of();
        }

        String normalizedQuery = normalizeQuery(query.trim());
        log.debug("Searching for foods with normalized query: {}", normalizedQuery);

        // Track search frequency for dynamic optimization
        cacheWarmingService.trackSearchFrequency(normalizedQuery);

        // Try exact match cache first (for product codes)
        if (isProductCode(normalizedQuery)) {
            List<FoodSearchResponseDto> exactMatch = getExactMatch(normalizedQuery);
            if (exactMatch != null && !exactMatch.isEmpty()) {
                log.debug("Found exact match in cache for: {}", normalizedQuery);
                return exactMatch;
            }
        }

        // Check if this is a popular search for extended caching
        boolean isPopular = cacheWarmingService.isPopularSearch(normalizedQuery);

        // Try regular search cache
        List<FoodSearchResponseDto> cachedResult = getSearchResult(normalizedQuery);
        if (cachedResult != null) {
            log.debug("Found search result in cache for: {} (popular: {})", normalizedQuery, isPopular);
            return cachedResult;
        }

        // Perform database search with optimized strategy
        log.debug("Performing database search for: {} (popular: {})", normalizedQuery, isPopular);
        List<FoodNutrients> results = performOptimizedSearch(normalizedQuery);

        List<FoodSearchResponseDto> searchResults = results.stream()
                .map(this::mapToFoodSearchResponse)
                .collect(Collectors.toList());

        // Cache the result with dynamic TTL based on popularity
        cacheSearchResultWithDynamicTTL(normalizedQuery, searchResults, isPopular);

        return searchResults;
    }

    @Cacheable(value = "exactFoodMatch", key = "#query")
    public List<FoodSearchResponseDto> getExactMatch(String query) {
        // This will be populated by the cache or return null for cache miss
        return null;
    }

    // Removed - now using dynamic popularity tracking via CacheWarmingService

    @Cacheable(value = "foodSearch", key = "#query")
    public List<FoodSearchResponseDto> getSearchResult(String query) {
        // This will be populated by the cache or return null for cache miss
        return null;
    }

    private void cacheSearchResultWithDynamicTTL(String query, List<FoodSearchResponseDto> results, boolean isPopular) {
        String cacheKey = SEARCH_CACHE_PREFIX + generateCacheKey(query);

        // Dynamic TTL based on popularity
        long ttlHours = isPopular ? 6 : 1; // Popular searches cached for 6 hours, others for 1 hour
        redisTemplate.opsForValue().set(cacheKey, results, ttlHours, TimeUnit.HOURS);

        // If it's an exact match (single result with exact product name), cache longer
        if (results.size() == 1 && results.get(0).getProductName().toLowerCase().equals(query.toLowerCase())) {
            String exactKey = EXACT_MATCH_PREFIX + generateCacheKey(query);
            redisTemplate.opsForValue().set(exactKey, results, 24, TimeUnit.HOURS);
        }

        log.debug("Cached search result for '{}' with TTL: {} hours (popular: {})", query, ttlHours, isPopular);
    }

    // Removed - now using CacheWarmingService.trackSearchFrequency()

    private String normalizeQuery(String query) {
        return query.toLowerCase()
                .replaceAll("[^a-z0-9\\s]", "") // Remove special characters
                .replaceAll("\\s+", " ") // Normalize whitespace
                .trim();
    }

    private boolean isProductCode(String query) {
        // Check if query looks like a product code (numbers, specific patterns)
        return query.matches("^[0-9]{8,13}$") || // Barcode patterns
               query.matches("^[A-Z0-9]{6,12}$"); // Product code patterns
    }

    private String generateCacheKey(String query) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(query.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // Fallback to simple hash
            return String.valueOf(query.hashCode());
        }
    }

    private FoodSearchResponseDto mapToFoodSearchResponse(FoodNutrients foodNutrients) {
        return FoodSearchResponseDto.builder()
                .code(foodNutrients.getCode())
                .productName(foodNutrients.getProductName())
                .brandTag(foodNutrients.getBrandTag())
                .categoryTags(foodNutrients.getCategoryTags())
                .countryTags(foodNutrients.getCountryTags())
                .imageUrl(foodNutrients.getImageUrl())
                .energyKcal100g(getDouble(foodNutrients.getEnergyKcal100g()))
                .fat100g(getDouble(foodNutrients.getFat100g()))
                .proteins100g(getDouble(foodNutrients.getProteins100g()))
                .carbohydrates100g(getDouble(foodNutrients.getCarbohydrates100g()))
                .sugars100g(getDouble(foodNutrients.getSugars100g()))
                .salt100g(getDouble(foodNutrients.getSalt100g()))
                .sodium100g(getDouble(foodNutrients.getSodium100g()))
                .build();
    }

    private Double getDouble(BigDecimal value) {
        return value != null ? value.doubleValue() : null;
    }

    private List<FoodNutrients> performOptimizedSearch(String query) {
        // Strategy 1: Try exact code match first (fastest)
        if (isProductCode(query)) {
            List<FoodNutrients> exactMatch = foodNutrientsRepository.findByExactCode(query);
            if (!exactMatch.isEmpty()) {
                log.debug("Found exact code match for: {}", query);
                return exactMatch;
            }
        }

        // Strategy 2: Try prefix search for short queries (autocomplete scenario)
        if (query.length() <= 3) {
            List<FoodNutrients> prefixResults = foodNutrientsRepository.findByProductNamePrefix(query);
            if (!prefixResults.isEmpty()) {
                log.debug("Found prefix match for short query: {}", query);
                return prefixResults;
            }
        }

        // Strategy 3: Use optimized UNION ALL query for better performance
        List<FoodNutrients> optimizedResults = foodNutrientsRepository.searchFoodOptimized(query);
        if (!optimizedResults.isEmpty()) {
            log.debug("Found results using optimized search for: {}", query);
            return optimizedResults;
        }

        // Strategy 4: Fallback to original search method
        log.debug("Using fallback search method for: {}", query);
        return foodNutrientsRepository.searchFoodByText(query);
    }
}
