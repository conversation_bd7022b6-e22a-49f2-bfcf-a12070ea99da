package com.myfitnessjourney.backend_service.repository;

import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * Optimized repository for food search operations using covering indexes
 * and read-heavy workload optimizations.
 * 
 * This repository contains queries optimized for the new index strategy
 * implemented in migration 013-optimize-indexes-for-read-heavy-workload.yaml
 */
public interface OptimizedFoodNutrientsRepository extends JpaRepository<FoodNutrients, Long> {

    /**
     * Optimized full-text search using covering index for index-only scans
     * Uses idx_food_search_results_covering for maximum performance
     */
    @Query(value = """
        SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g,
               ts_rank_cd(search_vector, plainto_tsquery('english', :query)) as rank_score
        FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
        ORDER BY rank_score DESC
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> searchFoodOptimizedCovering(@Param("query") String query);

    /**
     * Optimized exact code lookup using unique constraint
     * No additional index needed - unique constraint provides optimal performance
     */
    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE code = :code
        LIMIT 1
        """, nativeQuery = true)
    List<FoodNutrients> findByExactCodeOptimized(@Param("code") String code);

    /**
     * Optimized autocomplete search using specialized autocomplete index
     * Uses idx_food_autocomplete_optimized for prefix matching
     */
    @Query(value = """
        SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g
        FROM food_nutrients
        WHERE lower(product_name) LIKE lower(:prefix) || '%'
          AND length(product_name) BETWEEN 3 AND 100
        ORDER BY length(product_name), product_name
        LIMIT 10
        """, nativeQuery = true)
    List<Object[]> findByProductNamePrefixOptimized(@Param("prefix") String prefix);

    /**
     * Optimized exact name search using normalized expression index
     * Uses idx_food_exact_name_normalized for case-insensitive exact matches
     */
    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE lower(trim(product_name)) = lower(trim(:productName))
        LIMIT 5
        """, nativeQuery = true)
    List<FoodNutrients> findByExactProductNameOptimized(@Param("productName") String productName);

    /**
     * Optimized fuzzy search using enhanced trigram index
     * Uses idx_food_fuzzy_search_optimized for similarity matching
     */
    @Query(value = """
        SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g,
               similarity(lower(product_name), lower(:query)) as fuzzy_score
        FROM food_nutrients
        WHERE similarity(lower(product_name), lower(:query)) > 0.3
          AND length(product_name) >= 3
        ORDER BY fuzzy_score DESC
        LIMIT 15
        """, nativeQuery = true)
    List<Object[]> searchFoodFuzzyOptimized(@Param("query") String query);

    /**
     * Optimized brand-based search using covering index
     * Uses idx_food_brand_optimized for brand filtering with nutrition data
     */
    @Query(value = """
        SELECT brand_tag, product_name, energy_kcal_100g, proteins_100g, code
        FROM food_nutrients
        WHERE brand_tag = :brandTag
          AND length(brand_tag) > 0
        ORDER BY product_name
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> findByBrandOptimized(@Param("brandTag") String brandTag);

    /**
     * Optimized category search using enhanced GIN index
     * Uses idx_food_category_enhanced for array operations
     */
    @Query(value = """
        SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g
        FROM food_nutrients
        WHERE :category = ANY(category_tags)
          AND array_length(category_tags, 1) > 0
        ORDER BY product_name
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> findByCategoryOptimized(@Param("category") String category);

    /**
     * Optimized nutrition-focused search using nutrition covering index
     * Uses idx_food_nutrition_search_covering for health-conscious searches
     */
    @Query(value = """
        SELECT energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g, product_name, code
        FROM food_nutrients
        WHERE energy_kcal_100g BETWEEN :minCalories AND :maxCalories
          AND proteins_100g >= :minProtein
          AND energy_kcal_100g IS NOT NULL 
          AND proteins_100g IS NOT NULL 
          AND carbohydrates_100g IS NOT NULL 
          AND fat_100g IS NOT NULL
        ORDER BY proteins_100g DESC, energy_kcal_100g ASC
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> findByNutritionRangeOptimized(
        @Param("minCalories") Double minCalories,
        @Param("maxCalories") Double maxCalories,
        @Param("minProtein") Double minProtein
    );

    /**
     * Optimized combined search using composite indexes
     * Uses idx_food_category_search_composite for category + full-text search
     */
    @Query(value = """
        SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g,
               ts_rank_cd(search_vector, plainto_tsquery('english', :query)) as rank_score
        FROM food_nutrients
        WHERE :category = ANY(category_tags)
          AND search_vector @@ plainto_tsquery('english', :query)
          AND category_tags IS NOT NULL 
          AND search_vector IS NOT NULL
        ORDER BY rank_score DESC
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> searchFoodByCategoryAndTextOptimized(
        @Param("query") String query, 
        @Param("category") String category
    );

    /**
     * Optimized brand nutrition search using composite index
     * Uses idx_food_brand_nutrition_composite for brand-specific nutrition filtering
     */
    @Query(value = """
        SELECT brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, product_name, code
        FROM food_nutrients
        WHERE brand_tag = :brandTag
          AND energy_kcal_100g BETWEEN :minCalories AND :maxCalories
          AND proteins_100g >= :minProtein
          AND brand_tag IS NOT NULL 
          AND energy_kcal_100g IS NOT NULL 
          AND proteins_100g IS NOT NULL
        ORDER BY proteins_100g DESC
        LIMIT 15
        """, nativeQuery = true)
    List<Object[]> findByBrandAndNutritionOptimized(
        @Param("brandTag") String brandTag,
        @Param("minCalories") Double minCalories,
        @Param("maxCalories") Double maxCalories,
        @Param("minProtein") Double minProtein
    );

    /**
     * Optimized hybrid search combining full-text and fuzzy matching
     * Uses multiple optimized indexes for comprehensive search results
     */
    @Query(value = """
        (
            SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g,
                   ts_rank_cd(search_vector, plainto_tsquery('english', :query)) as rank_score,
                   1.0 as fuzzy_score,
                   1 as search_type
            FROM food_nutrients
            WHERE search_vector @@ plainto_tsquery('english', :query)
            ORDER BY rank_score DESC
            LIMIT 15
        )
        UNION ALL
        (
            SELECT code, product_name, brand_tag, energy_kcal_100g, proteins_100g, carbohydrates_100g, fat_100g,
                   0.0 as rank_score,
                   similarity(lower(product_name), lower(:query)) as fuzzy_score,
                   2 as search_type
            FROM food_nutrients
            WHERE similarity(lower(product_name), lower(:query)) > 0.3
              AND length(product_name) >= 3
              AND NOT (search_vector @@ plainto_tsquery('english', :query))
            ORDER BY fuzzy_score DESC
            LIMIT 5
        )
        ORDER BY search_type, rank_score DESC, fuzzy_score DESC
        LIMIT 20
        """, nativeQuery = true)
    List<Object[]> searchFoodHybridOptimized(@Param("query") String query);

    /**
     * Get index usage statistics for monitoring
     * Useful for performance analysis and optimization validation
     */
    @Query(value = """
        SELECT 
            indexname,
            idx_scan as scans,
            idx_tup_read as tuples_read,
            idx_tup_fetch as tuples_fetched,
            ROUND((idx_tup_fetch::numeric / NULLIF(idx_tup_read, 0)) * 100, 2) as efficiency_percent
        FROM pg_stat_user_indexes 
        WHERE schemaname = 'public' 
          AND tablename = 'food_nutrients'
          AND idx_scan > 0
        ORDER BY idx_scan DESC
        """, nativeQuery = true)
    List<Object[]> getIndexUsageStatistics();
}
