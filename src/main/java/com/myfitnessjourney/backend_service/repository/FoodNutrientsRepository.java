package com.myfitnessjourney.backend_service.repository;

import com.myfitnessjourney.backend_service.entity.FoodNutrients;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface FoodNutrientsRepository extends JpaRepository<FoodNutrients, Long> {

    @Query(value = """
        SELECT *,
               CASE
                   WHEN search_vector @@ plainto_tsquery('english', :query)
                   THEN ts_rank_cd(search_vector, plainto_tsquery('english', :query))
                   ELSE 0.0
               END AS rank_score,
               similarity(product_name, :query) AS fuzzy_score
        FROM food_nutrients
        WHERE search_vector @@ plainto_tsquery('english', :query)
           OR similarity(product_name, :query) > 0.3
        ORDER BY
            rank_score DESC,
            fuzzy_score DESC
        LIMIT 20;
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodByText(@Param("query") String query);

    // Optimized exact match search for product codes
    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE code = :code
        LIMIT 1
        """, nativeQuery = true)
    List<FoodNutrients> findByExactCode(@Param("code") String code);

    // Fast prefix search for autocomplete (optimized to use new indexes)
    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE lower(product_name) LIKE lower(:prefix) || '%'
        ORDER BY
            CASE WHEN lower(product_name) = lower(:prefix) THEN 1 ELSE 2 END,
            length(product_name)
        LIMIT 10
        """, nativeQuery = true)
    List<FoodNutrients> findByProductNamePrefix(@Param("prefix") String prefix);

    // Optimized search with UNION ALL for better performance
    @Query(value = """
        (
            SELECT *,
                   ts_rank_cd(search_vector, plainto_tsquery('english', :query)) as rank_score,
                   1.0 as fuzzy_score,
                   1 as search_type
            FROM food_nutrients
            WHERE search_vector @@ plainto_tsquery('english', :query)
            ORDER BY rank_score DESC
            LIMIT 15
        )
        UNION ALL
        (
            SELECT *,
                   0.0 as rank_score,
                   similarity(product_name, :query) as fuzzy_score,
                   2 as search_type
            FROM food_nutrients
            WHERE similarity(product_name, :query) > 0.3
              AND NOT (search_vector @@ plainto_tsquery('english', :query))
            ORDER BY fuzzy_score DESC
            LIMIT 5
        )
        ORDER BY search_type, rank_score DESC, fuzzy_score DESC
        LIMIT 20
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodOptimized(@Param("query") String query);

    // Category-based search for better filtering
    @Query(value = """
        SELECT * FROM food_nutrients
        WHERE :category = ANY(category_tags)
          AND (search_vector @@ plainto_tsquery('english', :query)
               OR similarity(product_name, :query) > 0.2)
        ORDER BY
            ts_rank_cd(search_vector, plainto_tsquery('english', :query)) DESC,
            similarity(product_name, :query) DESC
        LIMIT 20
        """, nativeQuery = true)
    List<FoodNutrients> searchFoodByCategory(@Param("query") String query, @Param("category") String category);

}
